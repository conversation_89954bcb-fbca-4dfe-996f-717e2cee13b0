<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفصل الخامس: أجهزة التصوير النووي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        body {
            font-family: 'Tajawal', sans-serif;
        }
        .interactive-chart {
            transition: all 0.3s ease;
        }
        .interactive-chart:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .calculator-input {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 8px 12px;
            transition: border-color 0.3s;
        }
        .calculator-input:focus {
            border-color: #3b82f6;
            outline: none;
        }
        .solution-step {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            border-left: 4px solid #3b82f6;
            margin: 8px 0;
            padding: 12px;
            border-radius: 0 8px 8px 0;
        }
        .device-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .device-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        .spec-table {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .performance-indicator {
            position: relative;
            overflow: hidden;
        }
        .performance-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }
        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
</head>
<body class="bg-gray-50 text-gray-800">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-4xl font-bold text-blue-800 mb-6 border-b-4 border-blue-300 pb-4 flex items-center">
            <i class="fas fa-camera mr-4 text-blue-600"></i>
            الفصل الخامس: أجهزة التصوير النووي
        </h1>
        
        <!-- مقدمة شاملة -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-8 rounded-xl shadow-lg mb-8">
            <h2 class="text-3xl font-bold text-blue-700 mb-6 flex items-center">
                <i class="fas fa-info-circle mr-3"></i>
                مقدمة
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <p class="text-lg leading-relaxed mb-4 text-gray-700">
                        أجهزة التصوير النووي تمثل قمة التطور التكنولوجي في الطب النووي، حيث تجمع بين المبادئ الفيزيائية المتقدمة 
                        والتقنيات الهندسية المعقدة لإنتاج صور وظيفية دقيقة للأعضاء والأنسجة الحية. هذه الأجهزة تمكن الأطباء من 
                        رؤية العمليات البيولوجية والكيميائية الحيوية داخل الجسم بطريقة غير جراحية وآمنة.
                    </p>
                    <p class="text-lg leading-relaxed mb-4 text-gray-700">
                        في هذا الفصل، سنستكشف الأسس العلمية والتقنية لأجهزة التصوير النووي الرئيسية، بدءاً من كاميرا جاما 
                        التقليدية وصولاً إلى أحدث تقنيات PET/CT و SPECT/CT المتطورة. سنتعمق في فهم مبادئ عملها، 
                        مكوناتها الأساسية، خصائص أدائها، وتطبيقاتها السريرية المتنوعة.
                    </p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-indigo-800 mb-4">أهداف التعلم</h3>
                    <ul class="space-y-2 text-sm text-indigo-700">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            فهم المبادئ الفيزيائية لأجهزة التصوير النووي المختلفة
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            تحليل مكونات وخصائص أداء كل نوع من الأجهزة
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            مقارنة التقنيات المختلفة واختيار الأنسب للتطبيقات السريرية
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            إتقان حسابات الأداء وتحليل جودة الصورة
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            فهم التطورات الحديثة والاتجاهات المستقبلية
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- خريطة الفصل التفاعلية -->
        <div class="bg-white p-6 rounded-xl shadow-lg mb-8">
            <h3 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-sitemap mr-3 text-purple-600"></i>
                خريطة محتويات الفصل
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="device-card bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border-2 border-blue-200" onclick="scrollToSection('gamma-camera')">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-camera text-blue-600 text-2xl mr-3"></i>
                        <h4 class="font-bold text-blue-800">5.1 كاميرا جاما</h4>
                    </div>
                    <p class="text-sm text-blue-700">الأساس في التصوير النووي</p>
                </div>
                <div class="device-card bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg border-2 border-green-200" onclick="scrollToSection('spect')">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-sync-alt text-green-600 text-2xl mr-3"></i>
                        <h4 class="font-bold text-green-800">5.2 SPECT</h4>
                    </div>
                    <p class="text-sm text-green-700">التصوير المقطعي أحادي الفوتون</p>
                </div>
                <div class="device-card bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg border-2 border-purple-200" onclick="scrollToSection('pet')">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-atom text-purple-600 text-2xl mr-3"></i>
                        <h4 class="font-bold text-purple-800">5.3 PET</h4>
                    </div>
                    <p class="text-sm text-purple-700">التصوير بالإشعاع البوزيتروني</p>
                </div>
                <div class="device-card bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-lg border-2 border-orange-200" onclick="scrollToSection('hybrid')">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-layer-group text-orange-600 text-2xl mr-3"></i>
                        <h4 class="font-bold text-orange-800">5.4 التصوير المختلط</h4>
                    </div>
                    <p class="text-sm text-orange-700">PET/CT و SPECT/CT</p>
                </div>
                <div class="device-card bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-lg border-2 border-red-200" onclick="scrollToSection('quality')">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-cog text-red-600 text-2xl mr-3"></i>
                        <h4 class="font-bold text-red-800">5.5 ضمان الجودة</h4>
                    </div>
                    <p class="text-sm text-red-700">اختبارات الأداء والمعايرة</p>
                </div>
                <div class="device-card bg-gradient-to-br from-teal-50 to-teal-100 p-4 rounded-lg border-2 border-teal-200" onclick="scrollToSection('future')">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-rocket text-teal-600 text-2xl mr-3"></i>
                        <h4 class="font-bold text-teal-800">5.6 التطورات المستقبلية</h4>
                    </div>
                    <p class="text-sm text-teal-700">التقنيات الناشئة والذكاء الاصطناعي</p>
                </div>
            </div>
        </div>

        <!-- القسم 5.1: كاميرا جاما -->
        <section id="gamma-camera" class="mb-12">
            <h2 class="text-3xl font-bold text-blue-700 mb-6 pt-4 border-t-4 border-blue-300 flex items-center">
                <i class="fas fa-camera mr-4"></i>
                5.1 كاميرا جاما (Gamma Camera)
            </h2>
            
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.1.1 المبادئ الأساسية والتطور التاريخي</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div>
                        <p class="text-lg leading-relaxed mb-4">
                            كاميرا جاما، المعروفة أيضاً باسم كاميرا آنجر (نسبة إلى مخترعها هال آنجر عام 1957)، تعتبر حجر الأساس 
                            في التصوير النووي الحديث. تعمل هذه الكاميرا على مبدأ كشف أشعة جاما المنبعثة من النويدات المشعة 
                            المحقونة في جسم المريض وتحويلها إلى صورة ثنائية الأبعاد تعكس التوزيع الوظيفي للمادة المشعة.
                        </p>
                        <p class="text-lg leading-relaxed mb-4">
                            المبدأ الأساسي يعتمد على تحويل أشعة جاما إلى ضوء مرئي بواسطة بلورة تألقية، ثم تحويل هذا الضوء 
                            إلى إشارة كهربائية قابلة للمعالجة والتحليل. هذه العملية تتم بكفاءة عالية ودقة مكانية ممتازة، 
                            مما يجعل كاميرا جاما الأداة المثلى للتصوير الوظيفي.
                        </p>
                    </div>
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <h4 class="font-bold text-blue-800 mb-4">التطور التاريخي</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-center">
                                <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs mr-3">1957</span>
                                <span>هال آنجر يخترع أول كاميرا جاما</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs mr-3">1960s</span>
                                <span>تطوير المضاعفات الضوئية المتعددة</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs mr-3">1970s</span>
                                <span>إدخال الحاسوب في معالجة الصور</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs mr-3">1980s</span>
                                <span>تطوير كاميرات متعددة الرؤوس</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs mr-3">1990s</span>
                                <span>دمج التقنيات الرقمية المتقدمة</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs mr-3">2000s</span>
                                <span>كاميرات CZT والكواشف الرقمية</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- مخطط تفاعلي لمكونات كاميرا جاما -->
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h4 class="font-bold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-puzzle-piece mr-2"></i>
                        مكونات كاميرا جاما - مخطط تفاعلي
                    </h4>
                    <div class="relative">
                        <svg viewBox="0 0 800 400" class="w-full h-64 border rounded-lg bg-white">
                            <!-- المصفوف -->
                            <rect x="50" y="50" width="100" height="300" fill="#3B82F6" opacity="0.7" class="component" data-component="collimator"/>
                            <text x="100" y="210" text-anchor="middle" fill="white" font-weight="bold">المصفوف</text>
                            
                            <!-- البلورة التألقية -->
                            <rect x="170" y="100" width="20" height="200" fill="#10B981" opacity="0.8" class="component" data-component="crystal"/>
                            <text x="180" y="210" text-anchor="middle" fill="white" font-weight="bold" transform="rotate(-90 180 210)">البلورة</text>
                            
                            <!-- الدليل الضوئي -->
                            <rect x="210" y="120" width="30" height="160" fill="#F59E0B" opacity="0.7" class="component" data-component="lightguide"/>
                            <text x="225" y="210" text-anchor="middle" fill="white" font-weight="bold" transform="rotate(-90 225 210)">الدليل</text>
                            
                            <!-- المضاعفات الضوئية -->
                            <g class="component" data-component="pmt">
                                <circle cx="300" cy="150" r="25" fill="#8B5CF6" opacity="0.8"/>
                                <circle cx="350" cy="150" r="25" fill="#8B5CF6" opacity="0.8"/>
                                <circle cx="400" cy="150" r="25" fill="#8B5CF6" opacity="0.8"/>
                                <circle cx="300" cy="200" r="25" fill="#8B5CF6" opacity="0.8"/>
                                <circle cx="350" cy="200" r="25" fill="#8B5CF6" opacity="0.8"/>
                                <circle cx="400" cy="200" r="25" fill="#8B5CF6" opacity="0.8"/>
                                <circle cx="300" cy="250" r="25" fill="#8B5CF6" opacity="0.8"/>
                                <circle cx="350" cy="250" r="25" fill="#8B5CF6" opacity="0.8"/>
                                <circle cx="400" cy="250" r="25" fill="#8B5CF6" opacity="0.8"/>
                                <text x="350" y="280" text-anchor="middle" fill="#8B5CF6" font-weight="bold">PMTs</text>
                            </g>
                            
                            <!-- معالج الإشارة -->
                            <rect x="500" y="150" width="120" height="100" fill="#EF4444" opacity="0.7" class="component" data-component="processor"/>
                            <text x="560" y="205" text-anchor="middle" fill="white" font-weight="bold">معالج الإشارة</text>
                            
                            <!-- الحاسوب -->
                            <rect x="650" y="120" width="100" height="160" fill="#6B7280" opacity="0.8" class="component" data-component="computer"/>
                            <text x="700" y="205" text-anchor="middle" fill="white" font-weight="bold">الحاسوب</text>
                            
                            <!-- أشعة جاما -->
                            <g>
                                <line x1="20" y1="120" x2="45" y2="120" stroke="#FF6B6B" stroke-width="3" marker-end="url(#arrowhead)"/>
                                <line x1="20" y1="160" x2="45" y2="160" stroke="#FF6B6B" stroke-width="3" marker-end="url(#arrowhead)"/>
                                <line x1="20" y1="200" x2="45" y2="200" stroke="#FF6B6B" stroke-width="3" marker-end="url(#arrowhead)"/>
                                <line x1="20" y1="240" x2="45" y2="240" stroke="#FF6B6B" stroke-width="3" marker-end="url(#arrowhead)"/>
                                <line x1="20" y1="280" x2="45" y2="280" stroke="#FF6B6B" stroke-width="3" marker-end="url(#arrowhead)"/>
                                <text x="10" y="205" text-anchor="middle" fill="#FF6B6B" font-weight="bold" transform="rotate(-90 10 205)">أشعة جاما</text>
                            </g>
                            
                            <!-- تعريف السهم -->
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#FF6B6B"/>
                                </marker>
                            </defs>
                        </svg>
                        
                        <!-- معلومات المكونات -->
                        <div id="componentInfo" class="mt-4 p-4 bg-blue-50 rounded-lg hidden">
                            <h5 class="font-bold text-blue-800 mb-2" id="componentTitle"></h5>
                            <p class="text-sm text-blue-700" id="componentDescription"></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- مكونات كاميرا جاما بالتفصيل -->
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.1.2 المكونات الأساسية ووظائفها</h3>
                
                <!-- المصفوف (Collimator) -->
                <div class="mb-8 p-6 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                    <h4 class="text-xl font-bold text-blue-800 mb-4 flex items-center">
                        <i class="fas fa-filter mr-3"></i>
                        المصفوف (Collimator)
                    </h4>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <p class="text-lg leading-relaxed mb-4">
                                المصفوف هو المكون الأول والأهم في كاميرا جاما، وهو عبارة عن قطعة من الرصاص تحتوي على آلاف 
                                الثقوب الصغيرة المتوازية. وظيفته الأساسية هي السماح فقط لأشعة جاما التي تسير في اتجاهات محددة 
                                بالوصول إلى البلورة التألقية، مما يضمن الحصول على صورة واضحة ومحددة المعالم.
                            </p>
                            
                            <div class="bg-white p-4 rounded-lg mb-4">
                                <h5 class="font-bold text-gray-800 mb-2">أنواع المصفوفات:</h5>
                                <ul class="space-y-2 text-sm">
                                    <li class="flex items-center">
                                        <span class="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                                        <strong>المتوازي (Parallel Hole):</strong> الأكثر شيوعاً، يحافظ على الحجم الطبيعي
                                    </li>
                                    <li class="flex items-center">
                                        <span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                                        <strong>المتقارب (Converging):</strong> يكبر الصورة، مناسب للأعضاء الصغيرة
                                    </li>
                                    <li class="flex items-center">
                                        <span class="w-3 h-3 bg-purple-500 rounded-full mr-2"></span>
                                        <strong>المتباعد (Diverging):</strong> يصغر الصورة، مناسب للأعضاء الكبيرة
                                    </li>
                                    <li class="flex items-center">
                                        <span class="w-3 h-3 bg-orange-500 rounded-full mr-2"></span>
                                        <strong>الثقب الواحد (Pinhole):</strong> دقة عالية جداً، للأعضاء الصغيرة
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- جدول مقارنة المصفوفات -->
                            <div class="spec-table p-4 rounded-lg">
                                <h5 class="font-bold text-gray-800 mb-3">مقارنة خصائص المصفوفات</h5>
                                <div class="overflow-x-auto">
                                    <table class="w-full text-xs">
                                        <thead class="bg-gray-200">
                                            <tr>
                                                <th class="px-2 py-2 text-right">النوع</th>
                                                <th class="px-2 py-2 text-center">الدقة المكانية</th>
                                                <th class="px-2 py-2 text-center">الحساسية</th>
                                                <th class="px-2 py-2 text-center">التطبيق</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="border-b">
                                                <td class="px-2 py-2 font-medium">LEHR</td>
                                                <td class="px-2 py-2 text-center">ممتازة</td>
                                                <td class="px-2 py-2 text-center">منخفضة</td>
                                                <td class="px-2 py-2 text-center">الغدة الدرقية</td>
                                            </tr>
                                            <tr class="border-b">
                                                <td class="px-2 py-2 font-medium">LEGP</td>
                                                <td class="px-2 py-2 text-center">جيدة</td>
                                                <td class="px-2 py-2 text-center">متوسطة</td>
                                                <td class="px-2 py-2 text-center">العظام</td>
                                            </tr>
                                            <tr class="border-b">
                                                <td class="px-2 py-2 font-medium">LEAP</td>
                                                <td class="px-2 py-2 text-center">متوسطة</td>
                                                <td class="px-2 py-2 text-center">عالية</td>
                                                <td class="px-2 py-2 text-center">القلب</td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 py-2 font-medium">MEGP</td>
                                                <td class="px-2 py-2 text-center">متوسطة</td>
                                                <td class="px-2 py-2 text-center">عالية</td>
                                                <td class="px-2 py-2 text-center">I-131</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- معادلة الدقة المكانية -->
                            <div class="bg-white p-4 rounded-lg border-2 border-blue-200">
                                <h5 class="font-bold text-blue-800 mb-2">معادلة الدقة المكانية:</h5>
                                <div class="text-center">
                                    <p class="font-mono text-lg">$R = d \left( \frac{L_e + b}{L_e} \right)$</p>
                                    <div class="text-xs text-gray-600 mt-2">
                                        <p>حيث: R = الدقة المكانية، d = قطر الثقب</p>
                                        <p>L_e = الطول الفعال، b = المسافة من المصفوف</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- البلورة التألقية -->
                <div class="mb-8 p-6 bg-green-50 rounded-lg border-l-4 border-green-500">
                    <h4 class="text-xl font-bold text-green-800 mb-4 flex items-center">
                        <i class="fas fa-gem mr-3"></i>
                        البلورة التألقية (Scintillation Crystal)
                    </h4>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <p class="text-lg leading-relaxed mb-4">
                                البلورة التألقية، عادة من يوديد الصوديوم المنشط بالثاليوم NaI(Tl)، هي قلب كاميرا جاما. 
                                تقوم بتحويل أشعة جاما غير المرئية إلى فوتونات ضوئية مرئية يمكن كشفها بواسطة المضاعفات الضوئية. 
                                هذه العملية تتم بكفاءة عالية تصل إلى 13% من طاقة أشعة جاما.
                            </p>
                            
                            <div class="bg-white p-4 rounded-lg">
                                <h5 class="font-bold text-gray-800 mb-2">خصائص NaI(Tl):</h5>
                                <ul class="space-y-1 text-sm">
                                    <li>• <strong>الكثافة:</strong> 3.67 g/cm³</li>
                                    <li>• <strong>العدد الذري الفعال:</strong> 50</li>
                                    <li>• <strong>الإنتاجية الضوئية:</strong> 38,000 فوتون/MeV</li>
                                    <li>• <strong>ذروة الانبعاث:</strong> 415 nm</li>
                                    <li>• <strong>زمن الاضمحلال:</strong> 230 ns</li>
                                    <li>• <strong>مؤشر الانكسار:</strong> 1.85</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div>
                            <!-- رسم بياني لطيف الانبعاث -->
                            <div class="bg-white p-4 rounded-lg">
                                <h5 class="font-bold text-gray-800 mb-3">طيف الانبعاث الضوئي لـ NaI(Tl)</h5>
                                <canvas id="emissionSpectrum" width="300" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المضاعفات الضوئية -->
                <div class="mb-8 p-6 bg-purple-50 rounded-lg border-l-4 border-purple-500">
                    <h4 class="text-xl font-bold text-purple-800 mb-4 flex items-center">
                        <i class="fas fa-bolt mr-3"></i>
                        المضاعفات الضوئية (Photomultiplier Tubes - PMTs)
                    </h4>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <p class="text-lg leading-relaxed mb-4">
                                المضاعفات الضوئية هي مصفوفة من الأنابيب الحساسة للضوء (عادة 37-91 أنبوب) مرتبة في نمط سداسي 
                                خلف البلورة التألقية. تقوم بتحويل الفوتونات الضوئية إلى إشارات كهربائية وتضخيمها بمعامل يصل 
                                إلى 10⁶-10⁸، مما يجعل من الممكن كشف الأحداث الفردية لأشعة جاما.
                            </p>
                            
                            <div class="bg-white p-4 rounded-lg mb-4">
                                <h5 class="font-bold text-gray-800 mb-2">عملية التضخيم:</h5>
                                <ol class="space-y-1 text-sm list-decimal list-inside">
                                    <li>الفوتون الضوئي يصطدم بالكاثود الضوئي</li>
                                    <li>ينبعث إلكترون ضوئي واحد</li>
                                    <li>الإلكترون يتسارع نحو القطب الأول (Dynode)</li>
                                    <li>ينتج 3-5 إلكترونات ثانوية</li>
                                    <li>العملية تتكرر عبر 10-14 قطب</li>
                                    <li>النتيجة: تضخيم هائل للإشارة</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div>
                            <!-- حاسبة تضخيم PMT -->
                            <div class="bg-white p-4 rounded-lg">
                                <h5 class="font-bold text-gray-800 mb-3">حاسبة تضخيم PMT</h5>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">عدد الأقطاب</label>
                                        <input type="range" id="dynodeCount" min="8" max="16" value="12" class="w-full" oninput="calculatePMTGain()">
                                        <span id="dynodeValue" class="text-sm text-gray-600">12</span>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">معامل الانبعاث الثانوي</label>
                                        <input type="range" id="deltaValue" min="2" max="6" value="4" step="0.1" class="w-full" oninput="calculatePMTGain()">
                                        <span id="deltaDisplay" class="text-sm text-gray-600">4.0</span>
                                    </div>
                                    <div class="performance-indicator bg-purple-100 p-3 rounded-lg">
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-purple-600" id="gainResult">16,777,216</div>
                                            <div class="text-sm text-gray-600">معامل التضخيم</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- مثال عملي: حساب موقع الحدث -->
            <div class="bg-gradient-to-r from-cyan-50 to-blue-50 p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-cyan-800 mb-6 flex items-center">
                    <i class="fas fa-calculator mr-3"></i>
                    مثال عملي: حساب موقع الحدث في كاميرا جاما
                </h3>

                <div class="bg-white p-6 rounded-lg shadow-md mb-6">
                    <h4 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                        <span class="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">1</span>
                        حساب الإحداثيات باستخدام خوارزمية آنجر
                    </h4>
                    
                    <div class="bg-cyan-50 p-4 rounded-lg mb-4">
                        <h5 class="font-bold text-cyan-800 mb-2">المعطيات:</h5>
                        <ul class="text-cyan-700 text-sm">
                            <li>• كاميرا جاما بـ 37 PMT مرتبة في نمط سداسي</li>
                            <li>• حدث أشعة جاما بطاقة 140 keV (Tc-99m)</li>
                            <li>• إشارات PMTs المجاورة للحدث</li>
                        </ul>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="solution-step">
                                <h6 class="font-bold text-gray-800 mb-2">الخطوة 1: جمع إشارات PMTs</h6>
                                <p class="text-sm">افترض أن PMTs المجاورة أعطت الإشارات التالية:</p>
                                <div class="grid grid-cols-3 gap-2 mt-2 text-xs">
                                    <div class="bg-blue-100 p-2 rounded text-center">PMT₁: 850</div>
                                    <div class="bg-blue-200 p-2 rounded text-center">PMT₂: 1200</div>
                                    <div class="bg-blue-100 p-2 rounded text-center">PMT₃: 650</div>
                                    <div class="bg-blue-200 p-2 rounded text-center">PMT₄: 1100</div>
                                    <div class="bg-blue-300 p-2 rounded text-center">PMT₅: 1500</div>
                                    <div class="bg-blue-200 p-2 rounded text-center">PMT₆: 900</div>
                                    <div class="bg-blue-100 p-2 rounded text-center">PMT₇: 400</div>
                                    <div class="bg-blue-200 p-2 rounded text-center">PMT₈: 750</div>
                                    <div class="bg-blue-100 p-2 rounded text-center">PMT₉: 300</div>
                                </div>
                            </div>

                            <div class="solution-step">
                                <h6 class="font-bold text-gray-800 mb-2">الخطوة 2: حساب الإحداثي X</h6>
                                <p class="text-sm">$X = \frac{\sum_{i} X_i \cdot S_i}{\sum_{i} S_i}$</p>
                                <p class="text-sm">حيث X_i = إحداثي PMT، S_i = إشارة PMT</p>
                            </div>

                            <div class="solution-step">
                                <h6 class="font-bold text-gray-800 mb-2">الخطوة 3: حساب الإحداثي Y</h6>
                                <p class="text-sm">$Y = \frac{\sum_{i} Y_i \cdot S_i}{\sum_{i} S_i}$</p>
                                <p class="text-sm">بنفس الطريقة للإحداثي Y</p>
                            </div>

                            <div class="solution-step">
                                <h6 class="font-bold text-gray-800 mb-2">الخطوة 4: حساب الطاقة الكلية</h6>
                                <p class="text-sm">$Z = \sum_{i} S_i = 7650$ (وحدة تعسفية)</p>
                                <p class="text-sm">هذه القيمة تستخدم لتمييز الطاقة وتصحيح التشتت</p>
                            </div>
                        </div>

                        <div>
                            <!-- حاسبة موقع الحدث التفاعلية -->
                            <div class="bg-white p-4 rounded-lg border-2 border-cyan-200">
                                <h5 class="font-bold text-cyan-800 mb-3">حاسبة موقع الحدث التفاعلية</h5>
                                <div class="space-y-3">
                                    <div class="grid grid-cols-3 gap-1" id="pmtGrid">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>
                                    <button onclick="calculateEventPosition()" class="w-full bg-cyan-600 text-white py-2 rounded-lg hover:bg-cyan-700 transition-colors">
                                        احسب موقع الحدث
                                    </button>
                                    <div id="positionResult" class="hidden">
                                        <div class="grid grid-cols-2 gap-2 text-sm">
                                            <div class="bg-cyan-50 p-2 rounded">
                                                <strong>الإحداثي X:</strong> <span id="xCoord"></span>
                                            </div>
                                            <div class="bg-cyan-50 p-2 rounded">
                                                <strong>الإحداثي Y:</strong> <span id="yCoord"></span>
                                            </div>
                                        </div>
                                        <div class="bg-cyan-100 p-2 rounded mt-2">
                                            <strong>الطاقة الكلية:</strong> <span id="totalEnergy"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- القسم 5.2: SPECT -->
        <section id="spect" class="mb-12">
            <h2 class="text-3xl font-bold text-green-700 mb-6 pt-4 border-t-4 border-green-300 flex items-center">
                <i class="fas fa-sync-alt mr-4"></i>
                5.2 التصوير المقطعي أحادي الفوتون (SPECT)
            </h2>
            
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.2.1 المبادئ الأساسية والتطور</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div>
                        <p class="text-lg leading-relaxed mb-4">
                            SPECT (Single Photon Emission Computed Tomography) هو تطوير متقدم لتقنية كاميرا جاما التقليدية، 
                            حيث تدور الكاميرا حول المريض لالتقاط صور من زوايا متعددة، مما يمكن من إعادة بناء صور مقطعية 
                            ثلاثية الأبعاد للتوزيع الوظيفي للنويدات المشعة داخل الجسم.
                        </p>
                        <p class="text-lg leading-relaxed mb-4">
                            هذه التقنية تتفوق على التصوير المستوي التقليدي بقدرتها على فصل الهياكل المتداخلة وتوفير 
                            معلومات دقيقة عن العمق والموقع ثلاثي الأبعاد، مما يحسن بشكل كبير من دقة التشخيص والتقييم الكمي.
                        </p>
                    </div>
                    <div class="bg-green-50 p-6 rounded-lg">
                        <h4 class="font-bold text-green-800 mb-4">مزايا SPECT على التصوير المستوي</h4>
                        <ul class="space-y-2 text-sm text-green-700">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                فصل الهياكل المتداخلة
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                تحسين التباين والدقة
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                التحليل الكمي الدقيق
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                تحديد موقع الآفات بدقة
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                تقليل الضوضاء الخلفية
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                إمكانية التصحيحات المتقدمة
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- مخطط دوران SPECT -->
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h4 class="font-bold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-redo mr-2"></i>
                        محاكاة دوران SPECT التفاعلية
                    </h4>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <div class="bg-white p-4 rounded-lg border-2 border-green-200">
                                <canvas id="spectRotation" width="300" height="300"></canvas>
                            </div>
                            <div class="mt-4 text-center">
                                <button onclick="startSPECTRotation()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors mr-2">
                                    <i class="fas fa-play mr-1"></i> بدء الدوران
                                </button>
                                <button onclick="stopSPECTRotation()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                                    <i class="fas fa-stop mr-1"></i> إيقاف
                                </button>
                            </div>
                        </div>
                        <div>
                            <h5 class="font-bold text-gray-800 mb-3">معاملات الاستحواذ</h5>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">عدد الإسقاطات</label>
                                    <input type="range" id="projectionCount" min="32" max="128" value="64" class="w-full" oninput="updateSPECTParams()">
                                    <span id="projectionValue" class="text-sm text-gray-600">64</span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">زاوية الدوران (درجة)</label>
                                    <input type="range" id="rotationAngle" min="180" max="360" value="360" class="w-full" oninput="updateSPECTParams()">
                                    <span id="angleValue" class="text-sm text-gray-600">360°</span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">زمن كل إسقاط (ثانية)</label>
                                    <input type="range" id="timePerProjection" min="10" max="60" value="20" class="w-full" oninput="updateSPECTParams()">
                                    <span id="timeValue" class="text-sm text-gray-600">20 ثانية</span>
                                </div>
                                <div class="bg-green-100 p-3 rounded-lg">
                                    <div class="text-center">
                                        <div class="text-lg font-bold text-green-600" id="totalTime">21.3 دقيقة</div>
                                        <div class="text-sm text-gray-600">الزمن الكلي للفحص</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- خوارزميات إعادة البناء -->
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.2.2 خوارزميات إعادة البناء</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-xl font-bold text-green-800 mb-4">الطرق التحليلية</h4>
                        
                        <!-- Filtered Back Projection -->
                        <div class="bg-green-50 p-4 rounded-lg mb-4">
                            <h5 class="font-bold text-green-700 mb-2">Filtered Back Projection (FBP)</h5>
                            <p class="text-sm text-green-600 mb-3">
                                الطريقة الكلاسيكية لإعادة البناء، سريعة ولكن حساسة للضوضاء.
                            </p>
                            <div class="bg-white p-3 rounded border">
                                <p class="text-xs font-mono">$f(x,y) = \int_0^{\pi} P_{\theta}(x\cos\theta + y\sin\theta) d\theta$</p>
                                <p class="text-xs text-gray-600 mt-1">حيث P_θ هو الإسقاط المرشح عند الزاوية θ</p>
                            </div>
                        </div>

                        <!-- المرشحات -->
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h5 class="font-bold text-blue-700 mb-2">أنواع المرشحات</h5>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• <strong>Ramp:</strong> مرشح أساسي، يضخم الضوضاء</li>
                                <li>• <strong>Hanning:</strong> يقلل الضوضاء، يقلل الدقة</li>
                                <li>• <strong>Hamming:</strong> توازن بين الضوضاء والدقة</li>
                                <li>• <strong>Butterworth:</strong> مرشح قابل للتعديل</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-xl font-bold text-purple-800 mb-4">الطرق التكرارية</h4>
                        
                        <!-- OSEM -->
                        <div class="bg-purple-50 p-4 rounded-lg mb-4">
                            <h5 class="font-bold text-purple-700 mb-2">OSEM (Ordered Subset EM)</h5>
                            <p class="text-sm text-purple-600 mb-3">
                                خوارزمية تكرارية متقدمة توفر جودة صورة أفضل وإمكانية تطبيق تصحيحات متعددة.
                            </p>
                            <div class="bg-white p-3 rounded border">
                                <p class="text-xs">المزايا:</p>
                                <ul class="text-xs text-gray-600 mt-1">
                                    <li>• تصحيح الامتصاص والتشتت</li>
                                    <li>• تحسين التباين</li>
                                    <li>• مرونة في التحكم</li>
                                </ul>
                            </div>
                        </div>

                        <!-- مقارنة الخوارزميات -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h5 class="font-bold text-gray-700 mb-2">مقارنة سريعة</h5>
                            <canvas id="algorithmComparison" width="300" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- مثال عملي: حساب دقة SPECT -->
            <div class="bg-gradient-to-r from-emerald-50 to-green-50 p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-emerald-800 mb-6 flex items-center">
                    <i class="fas fa-calculator mr-3"></i>
                    مثال عملي: حساب دقة SPECT المكانية
                </h3>

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h4 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                        <span class="bg-emerald-500 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">2</span>
                        تأثير المسافة على الدقة المكانية
                    </h4>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="bg-emerald-50 p-4 rounded-lg">
                                <h5 class="font-bold text-emerald-800 mb-2">المعطيات:</h5>
                                <ul class="text-emerald-700 text-sm">
                                    <li>• مصفوف LEHR (Low Energy High Resolution)</li>
                                    <li>• قطر الثقب: 1.5 mm</li>
                                    <li>• طول المصفوف: 35 mm</li>
                                    <li>• سمك الحاجز: 0.2 mm</li>
                                </ul>
                            </div>

                            <div class="solution-step">
                                <h6 class="font-bold text-gray-800 mb-2">معادلة الدقة المكانية:</h6>
                                <p class="text-sm">$R_{SPECT} = \sqrt{R_{intrinsic}^2 + R_{collimator}^2}$</p>
                                <p class="text-sm">$R_{collimator} = d \frac{(L_e + b)}{L_e}$</p>
                            </div>

                            <div class="solution-step">
                                <h6 class="font-bold text-gray-800 mb-2">الحساب عند مسافات مختلفة:</h6>
                                <div class="text-sm space-y-1">
                                    <p>• عند 0 cm: R = 3.8 mm</p>
                                    <p>• عند 5 cm: R = 5.2 mm</p>
                                    <p>• عند 10 cm: R = 6.9 mm</p>
                                    <p>• عند 15 cm: R = 8.7 mm</p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <!-- حاسبة دقة SPECT -->
                            <div class="bg-white p-4 rounded-lg border-2 border-emerald-200">
                                <h5 class="font-bold text-emerald-800 mb-3">حاسبة دقة SPECT</h5>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">قطر الثقب (mm)</label>
                                        <input type="number" id="holeSize" class="calculator-input w-full" value="1.5" step="0.1" oninput="calculateSPECTResolution()">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">طول المصفوف (mm)</label>
                                        <input type="number" id="collimatorLength" class="calculator-input w-full" value="35" oninput="calculateSPECTResolution()">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">المسافة من المصفوف (cm)</label>
                                        <input type="range" id="distanceSlider" min="0" max="20" value="10" class="w-full" oninput="calculateSPECTResolution()">
                                        <span id="distanceDisplay" class="text-sm text-gray-600">10 cm</span>
                                    </div>
                                    <div class="performance-indicator bg-emerald-100 p-3 rounded-lg">
                                        <div class="text-center">
                                            <div class="text-xl font-bold text-emerald-600" id="spectResolution">6.9 mm</div>
                                            <div class="text-sm text-gray-600">الدقة المكانية</div>
                                        </div>
                                    </div>
                                    <canvas id="resolutionChart" width="300" height="150"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- القسم 5.3: PET -->
        <section id="pet" class="mb-12">
            <h2 class="text-3xl font-bold text-purple-700 mb-6 pt-4 border-t-4 border-purple-300 flex items-center">
                <i class="fas fa-atom mr-4"></i>
                5.3 التصوير بالإشعاع البوزيتروني (PET)
            </h2>
            
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.3.1 المبادئ الفيزيائية الأساسية</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div>
                        <p class="text-lg leading-relaxed mb-4">
                            PET (Positron Emission Tomography) يعتمد على مبدأ فريد في الفيزياء النووية وهو إفناء البوزيترون-الإلكترون. 
                            عندما يتحلل نويدة مشعة باعثة للبوزيترون، ينبعث بوزيترون يسافر مسافة قصيرة في النسيج قبل أن يصطدم 
                            بإلكترون ويحدث إفناء متبادل ينتج عنه فوتونان بطاقة 511 keV لكل منهما في اتجاهين متضادين بزاوية 180°.
                        </p>
                        <p class="text-lg leading-relaxed mb-4">
                            هذه الخاصية الفريدة تمكن من الكشف المتزامن (Coincidence Detection) للفوتونين، مما يحدد خط الاستجابة 
                            (Line of Response - LOR) بدون الحاجة لمصفوف فيزيائي، وهذا ما يعطي PET حساسية ودقة فائقة مقارنة بـ SPECT.
                        </p>
                    </div>
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <h4 class="font-bold text-purple-800 mb-4">عملية الإفناء والكشف المتزامن</h4>
                        <div class="space-y-3">
                            <div class="bg-white p-3 rounded-lg border-2 border-purple-200">
                                <h5 class="font-bold text-purple-700 text-sm mb-2">1. انبعاث البوزيترون</h5>
                                <p class="text-xs text-purple-600">$^{18}F \rightarrow ^{18}O + e^+ + \nu_e$</p>
                            </div>
                            <div class="bg-white p-3 rounded-lg border-2 border-purple-200">
                                <h5 class="font-bold text-purple-700 text-sm mb-2">2. الإفناء</h5>
                                <p class="text-xs text-purple-600">$e^+ + e^- \rightarrow 2\gamma$ (511 keV each)</p>
                            </div>
                            <div class="bg-white p-3 rounded-lg border-2 border-purple-200">
                                <h5 class="font-bold text-purple-700 text-sm mb-2">3. الكشف المتزامن</h5>
                                <p class="text-xs text-purple-600">نافذة زمنية: 6-12 ns</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- محاكاة الإفناء والكشف المتزامن -->
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h4 class="font-bold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-atom mr-2"></i>
                        محاكاة الإفناء والكشف المتزامن
                    </h4>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <div class="bg-white p-4 rounded-lg border-2 border-purple-200">
                                <canvas id="annihilationDemo" width="300" height="300"></canvas>
                            </div>
                            <div class="mt-4 text-center">
                                <button onclick="startAnnihilationDemo()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors mr-2">
                                    <i class="fas fa-play mr-1"></i> بدء المحاكاة
                                </button>
                                <button onclick="resetAnnihilationDemo()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-redo mr-1"></i> إعادة تعيين
                                </button>
                            </div>
                        </div>
                        <div>
                            <h5 class="font-bold text-gray-800 mb-3">إحصائيات المحاكاة</h5>
                            <div class="space-y-3">
                                <div class="bg-white p-3 rounded-lg">
                                    <div class="flex justify-between">
                                        <span class="text-sm">الأحداث الحقيقية:</span>
                                        <span class="font-bold text-green-600" id="trueEvents">0</span>
                                    </div>
                                </div>
                                <div class="bg-white p-3 rounded-lg">
                                    <div class="flex justify-between">
                                        <span class="text-sm">الأحداث المتناثرة:</span>
                                        <span class="font-bold text-orange-600" id="scatteredEvents">0</span>
                                    </div>
                                </div>
                                <div class="bg-white p-3 rounded-lg">
                                    <div class="flex justify-between">
                                        <span class="text-sm">الأحداث العشوائية:</span>
                                        <span class="font-bold text-red-600" id="randomEvents">0</span>
                                    </div>
                                </div>
                                <div class="bg-purple-100 p-3 rounded-lg">
                                    <div class="flex justify-between">
                                        <span class="text-sm font-bold">نسبة الإشارة/الضوضاء:</span>
                                        <span class="font-bold text-purple-600" id="snrRatio">--</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- كواشف PET -->
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.3.2 كواشف PET والمتطلبات التقنية</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-xl font-bold text-purple-800 mb-4">المتطلبات الأساسية لكواشف PET</h4>
                        <div class="space-y-4">
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h5 class="font-bold text-purple-700 mb-2">السرعة العالية</h5>
                                <p class="text-sm text-purple-600">
                                    زمن اضمحلال قصير (&lt; 100 ns) لتقليل الأحداث العشوائية والحصول على دقة توقيت عالية.
                                </p>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h5 class="font-bold text-blue-700 mb-2">الكثافة العالية</h5>
                                <p class="text-sm text-blue-600">
                                    كثافة عالية وعدد ذري مرتفع لوقف فوتونات 511 keV بكفاءة عالية.
                                </p>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h5 class="font-bold text-green-700 mb-2">الإنتاجية الضوئية</h5>
                                <p class="text-sm text-green-600">
                                    إنتاجية ضوئية عالية لتحسين دقة الطاقة والإحصائيات.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <!-- جدول مقارنة كواشف PET -->
                        <div class="spec-table p-4 rounded-lg">
                            <h5 class="font-bold text-gray-800 mb-3">مقارنة كواشف PET الرئيسية</h5>
                            <div class="overflow-x-auto">
                                <table class="w-full text-xs">
                                    <thead class="bg-gray-200">
                                        <tr>
                                            <th class="px-2 py-2 text-right">الكاشف</th>
                                            <th class="px-2 py-2 text-center">الكثافة<br>(g/cm³)</th>
                                            <th class="px-2 py-2 text-center">زمن الاضمحلال<br>(ns)</th>
                                            <th class="px-2 py-2 text-center">الإنتاجية الضوئية<br>(فوتون/MeV)</th>
                                            <th class="px-2 py-2 text-center">التطبيق</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="border-b">
                                            <td class="px-2 py-2 font-medium">LSO</td>
                                            <td class="px-2 py-2 text-center">7.40</td>
                                            <td class="px-2 py-2 text-center">40</td>
                                            <td class="px-2 py-2 text-center">30,000</td>
                                            <td class="px-2 py-2 text-center">PET عالي الأداء</td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="px-2 py-2 font-medium">LYSO</td>
                                            <td class="px-2 py-2 text-center">7.25</td>
                                            <td class="px-2 py-2 text-center">41</td>
                                            <td class="px-2 py-2 text-center">32,000</td>
                                            <td class="px-2 py-2 text-center">PET متقدم</td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="px-2 py-2 font-medium">BGO</td>
                                            <td class="px-2 py-2 text-center">7.13</td>
                                            <td class="px-2 py-2 text-center">300</td>
                                            <td class="px-2 py-2 text-center">8,200</td>
                                            <td class="px-2 py-2 text-center">PET اقتصادي</td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="px-2 py-2 font-medium">GSO</td>
                                            <td class="px-2 py-2 text-center">6.71</td>
                                            <td class="px-2 py-2 text-center">60</td>
                                            <td class="px-2 py-2 text-center">12,500</td>
                                            <td class="px-2 py-2 text-center">PET متوسط</td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 py-2 font-medium">LuAP</td>
                                            <td class="px-2 py-2 text-center">8.34</td>
                                            <td class="px-2 py-2 text-center">17</td>
                                            <td class="px-2 py-2 text-center">11,500</td>
                                            <td class="px-2 py-2 text-center">PET فائق السرعة</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- مثال عملي: حساب دقة التوقيت -->
            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-indigo-800 mb-6 flex items-center">
                    <i class="fas fa-stopwatch mr-3"></i>
                    مثال عملي: حساب دقة التوقيت في PET
                </h3>

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h4 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                        <span class="bg-indigo-500 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">3</span>
                        Time-of-Flight PET وتحسين جودة الصورة
                    </h4>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="bg-indigo-50 p-4 rounded-lg">
                                <h5 class="font-bold text-indigo-800 mb-2">المعطيات:</h5>
                                <ul class="text-indigo-700 text-sm">
                                    <li>• دقة التوقيت: 500 ps FWHM</li>
                                    <li>• قطر المريض: 40 cm</li>
                                    <li>• سرعة الضوء: 3×10⁸ m/s</li>
                                    <li>• كاشف LYSO</li>
                                </ul>
                            </div>

                            <div class="solution-step">
                                <h6 class="font-bold text-gray-800 mb-2">حساب دقة الموقع:</h6>
                                <p class="text-sm">$\Delta x = \frac{c \cdot \Delta t}{2}$</p>
                                <p class="text-sm">$\Delta x = \frac{3 \times 10^8 \times 500 \times 10^{-12}}{2} = 7.5$ cm</p>
                            </div>

                            <div class="solution-step">
                                <h6 class="font-bold text-gray-800 mb-2">تحسين الإشارة/الضوضاء:</h6>
                                <p class="text-sm">$SNR_{improvement} = \sqrt{\frac{D}{\Delta x}}$</p>
                                <p class="text-sm">$SNR_{improvement} = \sqrt{\frac{40}{7.5}} = 2.31$</p>
                            </div>
                        </div>

                        <div>
                            <!-- حاسبة TOF PET -->
                            <div class="bg-white p-4 rounded-lg border-2 border-indigo-200">
                                <h5 class="font-bold text-indigo-800 mb-3">حاسبة TOF PET</h5>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">دقة التوقيت (ps)</label>
                                        <input type="range" id="timingResolution" min="200" max="1000" value="500" step="50" class="w-full" oninput="calculateTOF()">
                                        <span id="timingValue" class="text-sm text-gray-600">500 ps</span>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">قطر المريض (cm)</label>
                                        <input type="range" id="patientDiameter" min="20" max="60" value="40" class="w-full" oninput="calculateTOF()">
                                        <span id="diameterValue" class="text-sm text-gray-600">40 cm</span>
                                    </div>
                                    <div class="grid grid-cols-2 gap-2">
                                        <div class="performance-indicator bg-indigo-100 p-3 rounded-lg">
                                            <div class="text-center">
                                                <div class="text-lg font-bold text-indigo-600" id="spatialResolution">7.5 cm</div>
                                                <div class="text-xs text-gray-600">دقة الموقع</div>
                                            </div>
                                        </div>
                                        <div class="performance-indicator bg-green-100 p-3 rounded-lg">
                                            <div class="text-center">
                                                <div class="text-lg font-bold text-green-600" id="snrImprovement">2.31</div>
                                                <div class="text-xs text-gray-600">تحسين SNR</div>
                                            </div>
                                        </div>
                                    </div>
                                    <canvas id="tofChart" width="300" height="150"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- القسم 5.4: التصوير المختلط -->
        <section id="hybrid" class="mb-12">
            <h2 class="text-3xl font-bold text-orange-700 mb-6 pt-4 border-t-4 border-orange-300 flex items-center">
                <i class="fas fa-layer-group mr-4"></i>
                5.4 أنظمة التصوير المختلط (Hybrid Imaging)
            </h2>
            
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.4.1 PET/CT - دمج الوظيفة والتشريح</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div>
                        <p class="text-lg leading-relaxed mb-4">
                            أنظمة PET/CT تمثل ثورة حقيقية في التصوير الطبي، حيث تجمع بين المعلومات الوظيفية عالية الحساسية 
                            من PET والمعلومات التشريحية عالية الدقة من CT في فحص واحد. هذا الدمج يوفر تشخيصاً أكثر دقة 
                            وشمولية، خاصة في مجال الأورام وأمراض القلب والأعصاب.
                        </p>
                        <p class="text-lg leading-relaxed mb-4">
                            المزايا الرئيسية تشمل تحسين دقة التشخيص، تصحيح الامتصاص الدقيق، تحديد موقع الآفات بدقة عالية، 
                            وتقليل زمن الفحص الإجمالي. كما يمكن استخدام بيانات CT لتصحيحات متقدمة مثل تصحيح الامتصاص 
                            والتشتت في صور PET.
                        </p>
                    </div>
                    <div class="bg-orange-50 p-6 rounded-lg">
                        <h4 class="font-bold text-orange-800 mb-4">مزايا PET/CT</h4>
                        <ul class="space-y-2 text-sm text-orange-700">
                            <li class="flex items-center">
                                <i class="fas fa-check text-orange-500 mr-2"></i>
                                دقة تشخيصية أعلى (>95% في بعض الحالات)
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-orange-500 mr-2"></i>
                                تصحيح امتصاص دقيق ومتجانس
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-orange-500 mr-2"></i>
                                تحديد موقع دقيق للآفات
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-orange-500 mr-2"></i>
                                تقليل الفحوصات المتعددة
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-orange-500 mr-2"></i>
                                تحسين التخطيط العلاجي
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-orange-500 mr-2"></i>
                                متابعة الاستجابة للعلاج
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- مقارنة تفاعلية بين الأنظمة -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h4 class="font-bold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-balance-scale mr-2"></i>
                        مقارنة تفاعلية بين أنظمة التصوير
                    </h4>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <div class="space-y-3">
                                <button onclick="showComparison('sensitivity')" class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                    مقارنة الحساسية
                                </button>
                                <button onclick="showComparison('resolution')" class="w-full bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors">
                                    مقارنة الدقة المكانية
                                </button>
                                <button onclick="showComparison('cost')" class="w-full bg-purple-500 text-white py-2 rounded-lg hover:bg-purple-600 transition-colors">
                                    مقارنة التكلفة
                                </button>
                                <button onclick="showComparison('applications')" class="w-full bg-orange-500 text-white py-2 rounded-lg hover:bg-orange-600 transition-colors">
                                    مقارنة التطبيقات
                                </button>
                            </div>
                        </div>
                        <div>
                            <canvas id="hybridComparison" width="300" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SPECT/CT -->
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.4.2 SPECT/CT - تحسين دقة التشخيص</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <p class="text-lg leading-relaxed mb-4">
                            أنظمة SPECT/CT توفر حلاً اقتصادياً وفعالاً للتصوير المختلط، خاصة في التطبيقات التي لا تتطلب 
                            الحساسية الفائقة لـ PET. هذه الأنظمة مفيدة بشكل خاص في تصوير العظام، القلب، والغدد الصماء.
                        </p>
                        
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h5 class="font-bold text-green-700 mb-2">التطبيقات الرئيسية:</h5>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• تصوير العظام والمفاصل</li>
                                <li>• تصوير الغدة الدرقية والجار درقية</li>
                                <li>• تصوير القلب (MIBI, Thallium)</li>
                                <li>• تصوير الكلى والجهاز البولي</li>
                                <li>• تصوير الجهاز العصبي (DaTscan)</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div>
                        <!-- جدول مقارنة الأنظمة المختلطة -->
                        <div class="spec-table p-4 rounded-lg">
                            <h5 class="font-bold text-gray-800 mb-3">مقارنة الأنظمة المختلطة</h5>
                            <div class="overflow-x-auto">
                                <table class="w-full text-xs">
                                    <thead class="bg-gray-200">
                                        <tr>
                                            <th class="px-2 py-2 text-right">المعيار</th>
                                            <th class="px-2 py-2 text-center">PET/CT</th>
                                            <th class="px-2 py-2 text-center">SPECT/CT</th>
                                            <th class="px-2 py-2 text-center">PET/MRI</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="border-b">
                                            <td class="px-2 py-2 font-medium">الحساسية</td>
                                            <td class="px-2 py-2 text-center bg-green-100">ممتازة</td>
                                            <td class="px-2 py-2 text-center bg-yellow-100">جيدة</td>
                                            <td class="px-2 py-2 text-center bg-green-100">ممتازة</td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="px-2 py-2 font-medium">الدقة المكانية</td>
                                            <td class="px-2 py-2 text-center bg-green-100">4-6 mm</td>
                                            <td class="px-2 py-2 text-center bg-yellow-100">8-12 mm</td>
                                            <td class="px-2 py-2 text-center bg-green-100">4-6 mm</td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="px-2 py-2 font-medium">التكلفة</td>
                                            <td class="px-2 py-2 text-center bg-orange-100">عالية</td>
                                            <td class="px-2 py-2 text-center bg-green-100">متوسطة</td>
                                            <td class="px-2 py-2 text-center bg-red-100">عالية جداً</td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="px-2 py-2 font-medium">زمن الفحص</td>
                                            <td class="px-2 py-2 text-center bg-green-100">15-30 دقيقة</td>
                                            <td class="px-2 py-2 text-center bg-yellow-100">30-45 دقيقة</td>
                                            <td class="px-2 py-2 text-center bg-orange-100">45-90 دقيقة</td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 py-2 font-medium">التطبيق الرئيسي</td>
                                            <td class="px-2 py-2 text-center">الأورام</td>
                                            <td class="px-2 py-2 text-center">العظام، القلب</td>
                                            <td class="px-2 py-2 text-center">الدماغ، الأطفال</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- القسم 5.5: ضمان الجودة -->
        <section id="quality" class="mb-12">
            <h2 class="text-3xl font-bold text-red-700 mb-6 pt-4 border-t-4 border-red-300 flex items-center">
                <i class="fas fa-cog mr-4"></i>
                5.5 ضمان الجودة والأداء
            </h2>
            
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.5.1 اختبارات ضمان الجودة الأساسية</h3>
                
                <!-- جدول اختبارات ضمان الجودة التفاعلي -->
                <div class="bg-red-50 p-6 rounded-lg mb-6">
                    <h4 class="font-bold text-red-800 mb-4 flex items-center">
                        <i class="fas fa-clipboard-check mr-2"></i>
                        جدول اختبارات ضمان الجودة التفاعلي
                    </h4>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm" id="qcTable">
                            <thead class="bg-red-200">
                                <tr>
                                    <th class="px-4 py-3 text-right cursor-pointer" onclick="sortQCTable(0)">
                                        الاختبار <i class="fas fa-sort ml-1"></i>
                                    </th>
                                    <th class="px-4 py-3 text-center cursor-pointer" onclick="sortQCTable(1)">
                                        التكرار <i class="fas fa-sort ml-1"></i>
                                    </th>
                                    <th class="px-4 py-3 text-center cursor-pointer" onclick="sortQCTable(2)">
                                        المعيار المقبول <i class="fas fa-sort ml-1"></i>
                                    </th>
                                    <th class="px-4 py-3 text-center">الجهاز</th>
                                    <th class="px-4 py-3 text-center">الأهمية</th>
                                    <th class="px-4 py-3 text-center">الحالة</th>
                                </tr>
                            </thead>
                            <tbody id="qcTableBody">
                                <tr class="border-b hover:bg-red-50 transition-colors">
                                    <td class="px-4 py-3 font-medium">انتظام الاستجابة</td>
                                    <td class="px-4 py-3 text-center">يومي</td>
                                    <td class="px-4 py-3 text-center">±5%</td>
                                    <td class="px-4 py-3 text-center">جميع الأجهزة</td>
                                    <td class="px-4 py-3 text-center">
                                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">حرج</span>
                                    </td>
                                    <td class="px-4 py-3 text-center">
                                        <button onclick="toggleQCStatus(this)" class="bg-green-500 text-white px-2 py-1 rounded text-xs">مكتمل</button>
                                    </td>
                                </tr>
                                <tr class="border-b hover:bg-red-50 transition-colors">
                                    <td class="px-4 py-3 font-medium">دقة الطاقة</td>
                                    <td class="px-4 py-3 text-center">يومي</td>
                                    <td class="px-4 py-3 text-center">±10%</td>
                                    <td class="px-4 py-3 text-center">جاما، SPECT</td>
                                    <td class="px-4 py-3 text-center">
                                        <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs">مهم</span>
                                    </td>
                                    <td class="px-4 py-3 text-center">
                                        <button onclick="toggleQCStatus(this)" class="bg-green-500 text-white px-2 py-1 rounded text-xs">مكتمل</button>
                                    </td>
                                </tr>
                                <tr class="border-b hover:bg-red-50 transition-colors">
                                    <td class="px-4 py-3 font-medium">الدقة المكانية</td>
                                    <td class="px-4 py-3 text-center">أسبوعي</td>
                                    <td class="px-4 py-3 text-center">FWHM < 4mm</td>
                                    <td class="px-4 py-3 text-center">جاما، SPECT</td>
                                    <td class="px-4 py-3 text-center">
                                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">متوسط</span>
                                    </td>
                                    <td class="px-4 py-3 text-center">
                                        <button onclick="toggleQCStatus(this)" class="bg-yellow-500 text-white px-2 py-1 rounded text-xs">معلق</button>
                                    </td>
                                </tr>
                                <tr class="border-b hover:bg-red-50 transition-colors">
                                    <td class="px-4 py-3 font-medium">معايرة الكشف المتزامن</td>
                                    <td class="px-4 py-3 text-center">يومي</td>
                                    <td class="px-4 py-3 text-center">±5%</td>
                                    <td class="px-4 py-3 text-center">PET</td>
                                    <td class="px-4 py-3 text-center">
                                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">حرج</span>
                                    </td>
                                    <td class="px-4 py-3 text-center">
                                        <button onclick="toggleQCStatus(this)" class="bg-green-500 text-white px-2 py-1 rounded text-xs">مكتمل</button>
                                    </td>
                                </tr>
                                <tr class="border-b hover:bg-red-50 transition-colors">
                                    <td class="px-4 py-3 font-medium">معايرة التوقيت</td>
                                    <td class="px-4 py-3 text-center">أسبوعي</td>
                                    <td class="px-4 py-3 text-center">±2 ns</td>
                                    <td class="px-4 py-3 text-center">TOF-PET</td>
                                    <td class="px-4 py-3 text-center">
                                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">حرج</span>
                                    </td>
                                    <td class="px-4 py-3 text-center">
                                        <button onclick="toggleQCStatus(this)" class="bg-red-500 text-white px-2 py-1 rounded text-xs">فشل</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- إحصائيات ضمان الجودة -->
                    <div class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="bg-white p-4 rounded-lg shadow-md text-center">
                            <div class="text-2xl font-bold text-green-600" id="completedTests">3</div>
                            <div class="text-sm text-gray-600">اختبارات مكتملة</div>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-md text-center">
                            <div class="text-2xl font-bold text-yellow-600" id="pendingTests">1</div>
                            <div class="text-sm text-gray-600">اختبارات معلقة</div>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-md text-center">
                            <div class="text-2xl font-bold text-red-600" id="failedTests">1</div>
                            <div class="text-sm text-gray-600">اختبارات فاشلة</div>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-md text-center">
                            <div class="text-2xl font-bold text-blue-600" id="complianceRate">80%</div>
                            <div class="text-sm text-gray-600">معدل الامتثال</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- القسم 5.6: التطورات المستقبلية -->
        <section id="future" class="mb-12">
            <h2 class="text-3xl font-bold text-teal-700 mb-6 pt-4 border-t-4 border-teal-300 flex items-center">
                <i class="fas fa-rocket mr-4"></i>
                5.6 التطورات المستقبلية والتقنيات الناشئة
            </h2>
            
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.6.1 الذكاء الاصطناعي في التصوير النووي</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <p class="text-lg leading-relaxed mb-4">
                            الذكاء الاصطناعي والتعلم الآلي يحدثان ثورة في التصوير النووي، من تحسين جودة الصورة وتقليل الجرعة 
                            إلى التشخيص المساعد والتحليل الكمي المتقدم. هذه التقنيات تفتح آفاقاً جديدة لتحسين دقة التشخيص 
                            وكفاءة العمل السريري.
                        </p>
                        
                        <div class="bg-teal-50 p-4 rounded-lg">
                            <h5 class="font-bold text-teal-700 mb-2">تطبيقات الذكاء الاصطناعي:</h5>
                            <ul class="text-sm text-teal-600 space-y-1">
                                <li>• تحسين جودة الصورة بالشبكات العصبية</li>
                                <li>• الكشف التلقائي للآفات</li>
                                <li>• التحليل الكمي المتقدم</li>
                                <li>• تقليل الجرعة الإشعاعية</li>
                                <li>• التشخيص المساعد</li>
                                <li>• تحسين سير العمل</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div>
                        <!-- رسم بياني للتطورات المستقبلية -->
                        <div class="bg-teal-50 p-4 rounded-lg">
                            <h5 class="font-bold text-teal-800 mb-3">خريطة طريق التطوير</h5>
                            <canvas id="futureTimeline" width="300" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التقنيات الناشئة -->
            <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">5.6.2 التقنيات الناشئة</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="device-card bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg border-2 border-blue-200">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-microchip text-blue-600 text-3xl mr-3"></i>
                            <h4 class="font-bold text-blue-800">Digital PET</h4>
                        </div>
                        <p class="text-sm text-blue-700 mb-3">
                            كواشف رقمية مباشرة تحسن الحساسية ودقة التوقيت بشكل كبير.
                        </p>
                        <ul class="text-xs text-blue-600 space-y-1">
                            <li>• تحسين الحساسية بـ 40%</li>
                            <li>• دقة توقيت أفضل من 200 ps</li>
                            <li>• تقليل الجرعة الإشعاعية</li>
                        </ul>
                    </div>
                    
                    <div class="device-card bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-lg border-2 border-green-200">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-brain text-green-600 text-3xl mr-3"></i>
                            <h4 class="font-bold text-green-800">Total Body PET</h4>
                        </div>
                        <p class="text-sm text-green-700 mb-3">
                            تصوير الجسم كاملاً في وقت واحد بحساسية فائقة.
                        </p>
                        <ul class="text-xs text-green-600 space-y-1">
                            <li>• تصوير الجسم كاملاً (2 متر)</li>
                            <li>• حساسية أعلى بـ 40 مرة</li>
                            <li>• فحوصات ديناميكية متقدمة</li>
                        </ul>
                    </div>
                    
                    <div class="device-card bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-lg border-2 border-purple-200">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-dna text-purple-600 text-3xl mr-3"></i>
                            <h4 class="font-bold text-purple-800">Molecular Imaging</h4>
                        </div>
                        <p class="text-sm text-purple-700 mb-3">
                            تصوير العمليات الجزيئية والخلوية بدقة عالية.
                        </p>
                        <ul class="text-xs text-purple-600 space-y-1">
                            <li>• تتبع الخلايا الجذعية</li>
                            <li>• تصوير التعبير الجيني</li>
                            <li>• الطب الشخصي المتقدم</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- خلاصة الفصل -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-8 rounded-xl shadow-lg mb-8">
            <h3 class="text-2xl font-bold text-purple-800 mb-6 flex items-center">
                <i class="fas fa-graduation-cap mr-3"></i>
                خلاصة الفصل والنقاط الرئيسية
            </h3>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-bold text-blue-800 mb-3">المفاهيم الأساسية المكتسبة:</h4>
                    <ul class="space-y-2 text-sm text-blue-700">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            فهم عميق لمبادئ عمل أجهزة التصوير النووي المختلفة
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            إتقان حسابات الأداء والدقة لكل نوع من الأجهزة
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            القدرة على اختيار التقنية المناسبة للتطبيق السريري
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            فهم أهمية ضمان الجودة والمعايرة المستمرة
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                            الإلمام بالتطورات المستقبلية والتقنيات الناشئة
                        </li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-bold text-purple-800 mb-3">التطبيقات العملية:</h4>
                    <div class="space-y-3">
                        <div class="bg-white p-3 rounded-lg border-l-4 border-blue-500">
                            <h5 class="font-bold text-blue-700 text-sm">كاميرا جاما و SPECT</h5>
                            <p class="text-xs text-gray-600">العظام، القلب، الغدد الصماء، الكلى</p>
                        </div>
                        <div class="bg-white p-3 rounded-lg border-l-4 border-purple-500">
                            <h5 class="font-bold text-purple-700 text-sm">PET و PET/CT</h5>
                            <p class="text-xs text-gray-600">الأورام، أمراض القلب، الأعصاب</p>
                        </div>
                        <div class="bg-white p-3 rounded-lg border-l-4 border-green-500">
                            <h5 class="font-bold text-green-700 text-sm">التصوير المختلط</h5>
                            <p class="text-xs text-gray-600">تحسين الدقة التشخيصية والتخطيط العلاجي</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المراجع والمصادر -->
        <div class="bg-white p-8 rounded-xl shadow-lg mb-8">
            <h3 class="text-xl font-bold text-gray-700 mb-4">المراجع والمصادر المقترحة:</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <ul class="list-disc list-inside text-gray-600 space-y-1 text-sm">
                    <li>Cherry, S. R., Sorenson, J. A., & Phelps, M. E. (2012). <i>Physics in Nuclear Medicine</i>. 4th Edition. Elsevier.</li>
                    <li>Saha, G. B. (2016). <i>Physics and Radiobiology of Nuclear Medicine</i>. 4th Edition. Springer.</li>
                    <li>Wernick, M. N., & Aarsvold, J. N. (2004). <i>Emission Tomography: The Fundamentals of PET and SPECT</i>. Academic Press.</li>
                    <li>Bailey, D. L., Townsend, D. W., Valk, P. E., & Maisey, M. N. (2005). <i>Positron Emission Tomography</i>. Springer.</li>
                    <li>Zaidi, H. (2006). <i>Quantitative Analysis in Nuclear Medicine Imaging</i>. Springer.</li>
                    <li>Fahey, F. H. (2002). <i>Data Acquisition in PET Imaging</i>. Journal of Nuclear Medicine Technology.</li>
                </ul>
                <ul class="list-disc list-inside text-gray-600 space-y-1 text-sm">
                    <li>NEMA Standards Publications. <i>Performance Measurements of Gamma Cameras</i>. NEMA NU 1-2018.</li>
                    <li>NEMA Standards Publications. <i>Performance Measurements of Positron Emission Tomographs</i>. NEMA NU 2-2018.</li>
                    <li>IAEA Technical Reports Series No. 317. <i>Quality Assurance for SPECT Systems</i>.</li>
                    <li>AAPM Task Group 126. <i>PET/CT Acceptance Testing and Quality Assurance</i>.</li>
                    <li>European Association of Nuclear Medicine. <i>Guidelines for PET/CT Quality Control</i>.</li>
                    <li>Society of Nuclear Medicine. <i>Procedure Guidelines for Nuclear Medicine</i>.</li>
                </ul>
            </div>
        </div>

        <!-- أسئلة المراجعة والتقييم -->
        <div class="bg-blue-50 p-8 rounded-xl shadow-lg mb-8">
            <h4 class="font-bold text-blue-800 mb-4">أسئلة للمراجعة والتقييم:</h4>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <ol class="list-decimal list-inside text-sm text-blue-700 space-y-2">
                    <li>اشرح مبدأ عمل كاميرا جاما وكيفية تحديد موقع الحدث الإشعاعي.</li>
                    <li>قارن بين SPECT و PET من حيث المبادئ الفيزيائية والتطبيقات السريرية.</li>
                    <li>ما أهمية الكشف المتزامن في PET؟ وكيف يؤثر على جودة الصورة؟</li>
                    <li>اشرح كيف تؤثر دقة التوقيت على أداء TOF-PET.</li>
                    <li>ما مزايا الأنظمة المختلطة (PET/CT, SPECT/CT) على الأنظمة المنفردة؟</li>
                </ol>
                <ol class="list-decimal list-inside text-sm text-blue-700 space-y-2" start="6">
                    <li>اذكر أهم اختبارات ضمان الجودة لأجهزة التصوير النووي وتكرارها.</li>
                    <li>كيف يمكن للذكاء الاصطناعي تحسين التصوير النووي؟</li>
                    <li>قارن بين كواشف LSO و BGO في تطبيقات PET.</li>
                    <li>ما العوامل التي تؤثر على الدقة المكانية في SPECT؟</li>
                    <li>اشرح مفهوم Total Body PET ومزاياه المحتملة.</li>
                </ol>
            </div>
        </div>

        <!-- ملخص التحسينات المضافة -->
        <div class="bg-gradient-to-r from-green-50 to-blue-50 p-8 rounded-xl shadow-lg border-2 border-green-200">
            <h4 class="font-bold text-green-800 mb-4 flex items-center">
                <i class="fas fa-star mr-2"></i>
                التحسينات التفاعلية المضافة للفصل الخامس
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div>
                    <h5 class="font-bold text-blue-700 mb-2">الأمثلة العملية والمسائل المحلولة:</h5>
                    <ul class="text-blue-600 space-y-1">
                        <li>• حساب موقع الحدث في كاميرا جاما</li>
                        <li>• تحليل دقة SPECT المكانية</li>
                        <li>• حسابات TOF-PET ودقة التوقيت</li>
                        <li>• تقييم أداء الأنظمة المختلطة</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-bold text-purple-700 mb-2">الأدوات التفاعلية:</h5>
                    <ul class="text-purple-600 space-y-1">
                        <li>• محاكاة دوران SPECT</li>
                        <li>• محاكاة الإفناء في PET</li>
                        <li>• حاسبات الأداء المتقدمة</li>
                        <li>• جداول ضمان الجودة التفاعلية</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-bold text-green-700 mb-2">الرسوم البيانية والمحاكاة:</h5>
                    <ul class="text-green-600 space-y-1">
                        <li>• مخططات تفاعلية لمكونات الأجهزة</li>
                        <li>• رسوم بيانية لمقارنة الأداء</li>
                        <li>• محاكاة العمليات الفيزيائية</li>
                        <li>• خرائط طريق التطوير المستقبلي</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-bold text-orange-700 mb-2">المحتوى المتقدم:</h5>
                    <ul class="text-orange-600 space-y-1">
                        <li>• التقنيات الناشئة والذكاء الاصطناعي</li>
                        <li>• أنظمة ضمان الجودة الشاملة</li>
                        <li>• مقارنات تفصيلية بين التقنيات</li>
                        <li>• التطبيقات السريرية المتخصصة</li>
                    </ul>
                </div>
            </div>
            <div class="mt-4 p-3 bg-white rounded-lg">
                <p class="text-sm text-gray-700">
                    <strong>إجمالي التحسينات:</strong> تم إضافة 6 أقسام رئيسية، 4 أمثلة عملية محلولة، 
                    8 حاسبات ومحاكاة تفاعلية، 5 رسوم بيانية ديناميكية، وأنظمة ضمان جودة شاملة، 
                    مما يجعل الفصل مرجعاً شاملاً ومتقدماً في أجهزة التصوير النووي.
                </p>
            </div>
        </div>
        
    </div>

    <script>
        // متغيرات عامة
        let emissionChart = null;
        
        // بيانات مواقع PMTs (3x3 grid للتبسيط)
        const pmtPositions = [
            {x: -1, y: 1}, {x: 0, y: 1}, {x: 1, y: 1},
            {x: -1, y: 0}, {x: 0, y: 0}, {x: 1, y: 0},
            {x: -1, y: -1}, {x: 0, y: -1}, {x: 1, y: -1}
        ];

        // معلومات المكونات
        const componentInfo = {
            collimator: {
                title: "المصفوف (Collimator)",
                description: "يحتوي على آلاف الثقوب المتوازية لتوجيه أشعة جاما وتحسين الدقة المكانية. مصنوع من الرصاص لامتصاص الأشعة غير المرغوبة."
            },
            crystal: {
                title: "البلورة التألقية (NaI(Tl))",
                description: "تحول أشعة جاما إلى فوتونات ضوئية مرئية. سمكها عادة 9.5 مم وتوفر كفاءة كشف عالية للطاقات المنخفضة."
            },
            lightguide: {
                title: "الدليل الضوئي (Light Guide)",
                description: "ينقل الضوء من البلورة إلى المضاعفات الضوئية بكفاءة عالية ويوزعه بانتظام على PMTs المتعددة."
            },
            pmt: {
                title: "المضاعفات الضوئية (PMTs)",
                description: "مصفوفة من 37-91 أنبوب ضوئي تحول الفوتونات إلى إشارات كهربائية وتضخمها بمعامل 10⁶-10⁸."
            },
            processor: {
                title: "معالج الإشارة",
                description: "يحلل إشارات PMTs لحساب موقع وطاقة كل حدث، ويطبق تصحيحات الطاقة والخطية."
            },
            computer: {
                title: "نظام الحاسوب",
                description: "يجمع البيانات ويعالجها لإنتاج الصور النهائية، ويطبق تصحيحات متقدمة وخوارزميات إعادة البناء."
            }
        };

        // إنشاء طيف الانبعاث
        function createEmissionSpectrum() {
            const ctx = document.getElementById('emissionSpectrum').getContext('2d');
            
            // بيانات طيف NaI(Tl)
            const wavelengths = [];
            const intensities = [];
            
            for (let w = 350; w <= 550; w += 5) {
                wavelengths.push(w);
                // منحنى جاوسي مع ذروة عند 415 nm
                const intensity = Math.exp(-Math.pow((w - 415) / 40, 2));
                intensities.push(intensity * 100);
            }

            emissionChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: wavelengths,
                    datasets: [{
                        label: 'شدة الانبعاث',
                        data: intensities,
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'الطول الموجي (nm)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'الشدة النسبية (%)'
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 0
                        }
                    }
                }
            });
        }

        // حساب تضخيم PMT
        function calculatePMTGain() {
            const dynodes = parseInt(document.getElementById('dynodeCount').value);
            const delta = parseFloat(document.getElementById('deltaValue').value);
            
            document.getElementById('dynodeValue').textContent = dynodes;
            document.getElementById('deltaDisplay').textContent = delta.toFixed(1);
            
            const gain = Math.pow(delta, dynodes);
            document.getElementById('gainResult').textContent = gain.toLocaleString();
        }

        // إنشاء شبكة PMT
        function createPMTGrid() {
            const grid = document.getElementById('pmtGrid');
            grid.innerHTML = '';
            
            for (let i = 0; i < 9; i++) {
                const input = document.createElement('input');
                input.type = 'number';
                input.className = 'calculator-input text-center text-xs p-1';
                input.placeholder = '0';
                input.value = [850, 1200, 650, 1100, 1500, 900, 400, 750, 300][i];
                input.id = `pmt${i}`;
                grid.appendChild(input);
            }
        }

        // حساب موقع الحدث
        function calculateEventPosition() {
            let totalSignal = 0;
            let xWeighted = 0;
            let yWeighted = 0;
            
            for (let i = 0; i < 9; i++) {
                const signal = parseFloat(document.getElementById(`pmt${i}`).value) || 0;
                const pos = pmtPositions[i];
                
                totalSignal += signal;
                xWeighted += pos.x * signal;
                yWeighted += pos.y * signal;
            }
            
            const xCoord = totalSignal > 0 ? (xWeighted / totalSignal).toFixed(3) : 0;
            const yCoord = totalSignal > 0 ? (yWeighted / totalSignal).toFixed(3) : 0;
            
            document.getElementById('xCoord').textContent = xCoord;
            document.getElementById('yCoord').textContent = yCoord;
            document.getElementById('totalEnergy').textContent = totalSignal.toLocaleString();
            document.getElementById('positionResult').classList.remove('hidden');
        }

        // التفاعل مع مخطط المكونات
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء الرسوم البيانية
            createEmissionSpectrum();
            calculatePMTGain();
            createPMTGrid();
            
            // إضافة التفاعل مع مخطط المكونات
            const components = document.querySelectorAll('.component');
            const infoDiv = document.getElementById('componentInfo');
            const titleDiv = document.getElementById('componentTitle');
            const descDiv = document.getElementById('componentDescription');
            
            components.forEach(component => {
                component.addEventListener('mouseenter', function() {
                    const compType = this.getAttribute('data-component');
                    const info = componentInfo[compType];
                    
                    if (info) {
                        titleDiv.textContent = info.title;
                        descDiv.textContent = info.description;
                        infoDiv.classList.remove('hidden');
                    }
                });
                
                component.addEventListener('mouseleave', function() {
                    infoDiv.classList.add('hidden');
                });
            });
        });

        // التنقل السلس
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({
                behavior: 'smooth'
            });
        }

        // متغيرات إضافية للرسوم البيانية
        let spectRotationChart = null;
        let algorithmChart = null;
        let resolutionChart = null;
        let annihilationChart = null;
        let hybridChart = null;
        let futureChart = null;
        let spectRotationAngle = 0;
        let spectRotationInterval = null;

        // محاكاة دوران SPECT
        function startSPECTRotation() {
            if (spectRotationInterval) return;
            
            const canvas = document.getElementById('spectRotation');
            const ctx = canvas.getContext('2d');
            
            spectRotationInterval = setInterval(() => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // رسم المريض (دائرة في المركز)
                ctx.fillStyle = '#FCA5A5';
                ctx.beginPath();
                ctx.arc(150, 150, 40, 0, 2 * Math.PI);
                ctx.fill();
                
                // رسم الكاميرا (مستطيل دوار)
                ctx.save();
                ctx.translate(150, 150);
                ctx.rotate(spectRotationAngle * Math.PI / 180);
                ctx.fillStyle = '#3B82F6';
                ctx.fillRect(-10, -80, 20, 30);
                ctx.restore();
                
                // رسم مسار الدوران
                ctx.strokeStyle = '#E5E7EB';
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.arc(150, 150, 80, 0, 2 * Math.PI);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // رسم الزاوية الحالية
                ctx.fillStyle = '#374151';
                ctx.font = '14px Arial';
                ctx.fillText(`${spectRotationAngle.toFixed(0)}°`, 10, 20);
                
                spectRotationAngle += 2;
                if (spectRotationAngle >= 360) spectRotationAngle = 0;
            }, 50);
        }

        function stopSPECTRotation() {
            if (spectRotationInterval) {
                clearInterval(spectRotationInterval);
                spectRotationInterval = null;
            }
        }

        // تحديث معاملات SPECT
        function updateSPECTParams() {
            const projections = document.getElementById('projectionCount').value;
            const angle = document.getElementById('rotationAngle').value;
            const timePerProj = document.getElementById('timePerProjection').value;
            
            document.getElementById('projectionValue').textContent = projections;
            document.getElementById('angleValue').textContent = angle + '°';
            document.getElementById('timeValue').textContent = timePerProj + ' ثانية';
            
            const totalTime = (projections * timePerProj) / 60;
            document.getElementById('totalTime').textContent = totalTime.toFixed(1) + ' دقيقة';
        }

        // إنشاء رسم بياني لمقارنة الخوارزميات
        function createAlgorithmComparison() {
            const ctx = document.getElementById('algorithmComparison').getContext('2d');
            
            algorithmChart = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['السرعة', 'جودة الصورة', 'تقليل الضوضاء', 'الدقة', 'سهولة الاستخدام'],
                    datasets: [{
                        label: 'FBP',
                        data: [90, 60, 40, 70, 90],
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.2)'
                    }, {
                        label: 'OSEM',
                        data: [40, 90, 85, 90, 60],
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16, 185, 129, 0.2)'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        // حساب دقة SPECT
        function calculateSPECTResolution() {
            const holeSize = parseFloat(document.getElementById('holeSize').value);
            const collLength = parseFloat(document.getElementById('collimatorLength').value);
            const distance = parseFloat(document.getElementById('distanceSlider').value);
            
            document.getElementById('distanceDisplay').textContent = distance + ' cm';
            
            // حساب دقة المصفوف
            const collimatorRes = holeSize * (collLength + distance * 10) / collLength;
            
            // دقة النظام الكلية (افتراض دقة جوهرية 3.8 مم)
            const intrinsicRes = 3.8;
            const systemRes = Math.sqrt(Math.pow(intrinsicRes, 2) + Math.pow(collimatorRes, 2));
            
            document.getElementById('spectResolution').textContent = systemRes.toFixed(1) + ' mm';
            
            // تحديث الرسم البياني
            updateResolutionChart(distance, systemRes);
        }

        function updateResolutionChart(currentDistance, currentRes) {
            const ctx = document.getElementById('resolutionChart').getContext('2d');
            
            if (resolutionChart) {
                resolutionChart.destroy();
            }
            
            const distances = [];
            const resolutions = [];
            
            for (let d = 0; d <= 20; d += 2) {
                distances.push(d);
                const holeSize = parseFloat(document.getElementById('holeSize').value);
                const collLength = parseFloat(document.getElementById('collimatorLength').value);
                const collRes = holeSize * (collLength + d * 10) / collLength;
                const sysRes = Math.sqrt(Math.pow(3.8, 2) + Math.pow(collRes, 2));
                resolutions.push(sysRes);
            }
            
            resolutionChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: distances,
                    datasets: [{
                        label: 'الدقة المكانية (mm)',
                        data: resolutions,
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'المسافة (cm)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'الدقة (mm)'
                            }
                        }
                    }
                }
            });
        }

        // محاكاة الإفناء في PET
        let annihilationInterval = null;
        let trueCount = 0, scatteredCount = 0, randomCount = 0;

        function startAnnihilationDemo() {
            if (annihilationInterval) return;
            
            const canvas = document.getElementById('annihilationDemo');
            const ctx = canvas.getContext('2d');
            
            annihilationInterval = setInterval(() => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // رسم حلقة الكواشف
                ctx.strokeStyle = '#8B5CF6';
                ctx.lineWidth = 10;
                ctx.beginPath();
                ctx.arc(150, 150, 120, 0, 2 * Math.PI);
                ctx.stroke();
                
                // محاكاة حدث إفناء عشوائي
                const angle1 = Math.random() * 2 * Math.PI;
                const angle2 = angle1 + Math.PI + (Math.random() - 0.5) * 0.2; // انحراف طفيف
                
                const x1 = 150 + 120 * Math.cos(angle1);
                const y1 = 150 + 120 * Math.sin(angle1);
                const x2 = 150 + 120 * Math.cos(angle2);
                const y2 = 150 + 120 * Math.sin(angle2);
                
                // رسم خط الاستجابة
                ctx.strokeStyle = '#EF4444';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
                
                // رسم نقطة الإفناء
                ctx.fillStyle = '#F59E0B';
                ctx.beginPath();
                ctx.arc(150 + (Math.random() - 0.5) * 60, 150 + (Math.random() - 0.5) * 60, 3, 0, 2 * Math.PI);
                ctx.fill();
                
                // تحديث الإحصائيات
                const eventType = Math.random();
                if (eventType < 0.7) {
                    trueCount++;
                } else if (eventType < 0.9) {
                    scatteredCount++;
                } else {
                    randomCount++;
                }
                
                document.getElementById('trueEvents').textContent = trueCount;
                document.getElementById('scatteredEvents').textContent = scatteredCount;
                document.getElementById('randomEvents').textContent = randomCount;
                
                const snr = trueCount / (scatteredCount + randomCount + 1);
                document.getElementById('snrRatio').textContent = snr.toFixed(2);
                
            }, 200);
        }

        function resetAnnihilationDemo() {
            if (annihilationInterval) {
                clearInterval(annihilationInterval);
                annihilationInterval = null;
            }
            trueCount = scatteredCount = randomCount = 0;
            document.getElementById('trueEvents').textContent = '0';
            document.getElementById('scatteredEvents').textContent = '0';
            document.getElementById('randomEvents').textContent = '0';
            document.getElementById('snrRatio').textContent = '--';
            
            const canvas = document.getElementById('annihilationDemo');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // حساب TOF PET
        function calculateTOF() {
            const timing = parseFloat(document.getElementById('timingResolution').value);
            const diameter = parseFloat(document.getElementById('patientDiameter').value);
            
            document.getElementById('timingValue').textContent = timing + ' ps';
            document.getElementById('diameterValue').textContent = diameter + ' cm';
            
            // حساب دقة الموقع
            const spatialRes = (3e8 * timing * 1e-12) / 2 * 100; // تحويل إلى cm
            const snrImprovement = Math.sqrt(diameter / spatialRes);
            
            document.getElementById('spatialResolution').textContent = spatialRes.toFixed(1) + ' cm';
            document.getElementById('snrImprovement').textContent = snrImprovement.toFixed(2);
            
            updateTOFChart(timing, spatialRes);
        }

        function updateTOFChart(timing, spatialRes) {
            const ctx = document.getElementById('tofChart').getContext('2d');
            
            if (window.tofChart) {
                window.tofChart.destroy();
            }
            
            const timings = [];
            const resolutions = [];
            
            for (let t = 200; t <= 1000; t += 50) {
                timings.push(t);
                const res = (3e8 * t * 1e-12) / 2 * 100;
                resolutions.push(res);
            }
            
            window.tofChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: timings,
                    datasets: [{
                        label: 'دقة الموقع (cm)',
                        data: resolutions,
                        borderColor: '#8B5CF6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'دقة التوقيت (ps)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'دقة الموقع (cm)'
                            }
                        }
                    }
                }
            });
        }

        // مقارنة الأنظمة المختلطة
        function showComparison(type) {
            const ctx = document.getElementById('hybridComparison').getContext('2d');
            
            if (hybridChart) {
                hybridChart.destroy();
            }
            
            let data, title;
            
            switch(type) {
                case 'sensitivity':
                    data = {
                        labels: ['PET/CT', 'SPECT/CT', 'PET/MRI'],
                        datasets: [{
                            data: [95, 60, 95],
                            backgroundColor: ['#3B82F6', '#10B981', '#8B5CF6']
                        }]
                    };
                    title = 'مقارنة الحساسية (%)';
                    break;
                case 'resolution':
                    data = {
                        labels: ['PET/CT', 'SPECT/CT', 'PET/MRI'],
                        datasets: [{
                            data: [5, 10, 5],
                            backgroundColor: ['#3B82F6', '#10B981', '#8B5CF6']
                        }]
                    };
                    title = 'الدقة المكانية (mm)';
                    break;
                case 'cost':
                    data = {
                        labels: ['PET/CT', 'SPECT/CT', 'PET/MRI'],
                        datasets: [{
                            data: [100, 40, 150],
                            backgroundColor: ['#3B82F6', '#10B981', '#8B5CF6']
                        }]
                    };
                    title = 'التكلفة النسبية';
                    break;
                case 'applications':
                    data = {
                        labels: ['الأورام', 'القلب', 'العظام', 'الأعصاب'],
                        datasets: [{
                            label: 'PET/CT',
                            data: [95, 85, 60, 90],
                            backgroundColor: 'rgba(59, 130, 246, 0.7)'
                        }, {
                            label: 'SPECT/CT',
                            data: [70, 90, 95, 70],
                            backgroundColor: 'rgba(16, 185, 129, 0.7)'
                        }]
                    };
                    title = 'ملاءمة التطبيقات (%)';
                    break;
            }
            
            hybridChart = new Chart(ctx, {
                type: type === 'applications' ? 'bar' : 'doughnut',
                data: data,
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: title
                        }
                    }
                }
            });
        }

        // إدارة جدول ضمان الجودة
        function toggleQCStatus(button) {
            const currentStatus = button.textContent;
            const statuses = ['مكتمل', 'معلق', 'فشل'];
            const colors = ['green', 'yellow', 'red'];
            
            let currentIndex = statuses.indexOf(currentStatus);
            currentIndex = (currentIndex + 1) % statuses.length;
            
            button.textContent = statuses[currentIndex];
            button.className = `bg-${colors[currentIndex]}-500 text-white px-2 py-1 rounded text-xs`;
            
            updateQCStats();
        }

        function updateQCStats() {
            const buttons = document.querySelectorAll('#qcTableBody button');
            let completed = 0, pending = 0, failed = 0;
            
            buttons.forEach(button => {
                const status = button.textContent;
                if (status === 'مكتمل') completed++;
                else if (status === 'معلق') pending++;
                else if (status === 'فشل') failed++;
            });
            
            document.getElementById('completedTests').textContent = completed;
            document.getElementById('pendingTests').textContent = pending;
            document.getElementById('failedTests').textContent = failed;
            
            const total = buttons.length;
            const compliance = ((completed / total) * 100).toFixed(0);
            document.getElementById('complianceRate').textContent = compliance + '%';
        }

        function sortQCTable(columnIndex) {
            const table = document.getElementById('qcTableBody');
            const rows = Array.from(table.rows);
            
            rows.sort((a, b) => {
                const aVal = a.cells[columnIndex].textContent.trim();
                const bVal = b.cells[columnIndex].textContent.trim();
                return aVal.localeCompare(bVal);
            });
            
            rows.forEach(row => table.appendChild(row));
        }

        // رسم بياني للتطورات المستقبلية
        function createFutureTimeline() {
            const ctx = document.getElementById('futureTimeline').getContext('2d');
            
            futureChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['2020', '2022', '2024', '2026', '2028', '2030'],
                    datasets: [{
                        label: 'الذكاء الاصطناعي',
                        data: [20, 40, 70, 85, 95, 100],
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)'
                    }, {
                        label: 'Digital PET',
                        data: [5, 15, 35, 60, 80, 95],
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)'
                    }, {
                        label: 'Total Body PET',
                        data: [0, 5, 20, 40, 70, 90],
                        borderColor: '#8B5CF6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'انتشار التقنيات الجديدة (%)'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        // تهيئة شاملة للصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء جميع الرسوم البيانية
            createEmissionSpectrum();
            calculatePMTGain();
            createPMTGrid();
            updateSPECTParams();
            createAlgorithmComparison();
            calculateSPECTResolution();
            calculateTOF();
            showComparison('sensitivity');
            updateQCStats();
            createFutureTimeline();
            
            // إضافة التفاعل مع مخطط المكونات
            const components = document.querySelectorAll('.component');
            const infoDiv = document.getElementById('componentInfo');
            const titleDiv = document.getElementById('componentTitle');
            const descDiv = document.getElementById('componentDescription');
            
            components.forEach(component => {
                component.addEventListener('mouseenter', function() {
                    const compType = this.getAttribute('data-component');
                    const info = componentInfo[compType];
                    
                    if (info) {
                        titleDiv.textContent = info.title;
                        descDiv.textContent = info.description;
                        infoDiv.classList.remove('hidden');
                    }
                });
                
                component.addEventListener('mouseleave', function() {
                    infoDiv.classList.add('hidden');
                });
            });
        });
    </script>
</body>
</html>
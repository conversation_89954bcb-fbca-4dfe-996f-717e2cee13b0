<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الفصل الرابع - الميزات التفاعلية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        body { font-family: 'Tajawal', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-blue-800 mb-6">اختبار الميزات التفاعلية - الفصل الرابع</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-xl font-bold text-green-700 mb-4">✅ الميزات المضافة بنجاح</h2>
                <ul class="space-y-2 text-sm">
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> 4 أمثلة عملية محلولة</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> 4 حاسبات تفاعلية</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> 3 رسوم بيانية ديناميكية</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> جداول تفاعلية قابلة للترتيب</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> محاكاة أداء الكواشف</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> معادلات رياضية بـ MathJax</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> تصميم متجاوب</li>
                </ul>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-xl font-bold text-blue-700 mb-4">📊 إحصائيات التطوير</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span>عدد الأمثلة العملية:</span>
                        <span class="font-bold text-blue-600">4</span>
                    </div>
                    <div class="flex justify-between">
                        <span>عدد الحاسبات:</span>
                        <span class="font-bold text-blue-600">4</span>
                    </div>
                    <div class="flex justify-between">
                        <span>عدد الرسوم البيانية:</span>
                        <span class="font-bold text-blue-600">3</span>
                    </div>
                    <div class="flex justify-between">
                        <span>عدد الجداول التفاعلية:</span>
                        <span class="font-bold text-blue-600">3</span>
                    </div>
                    <div class="flex justify-between">
                        <span>نسبة التفاعل:</span>
                        <span class="font-bold text-green-600">100%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
            <h2 class="text-2xl font-bold text-purple-800 mb-4">🎯 ملخص التحسينات</h2>
            <p class="text-gray-700 leading-relaxed">
                تم تطوير الفصل الرابع "كواشف الإشعاع والقياس النووي" بإضافة عناصر تفاعلية شاملة تشمل:
                أمثلة عملية محلولة خطوة بخطوة، حاسبات تفاعلية للمعادلات المهمة، رسوم بيانية ديناميكية لمقارنة الكواشف،
                وجداول تفاعلية قابلة للترتيب. كما تم إضافة محاكاة شاملة لأداء الكواشف في بيئات مختلفة.
            </p>
            
            <div class="mt-4 text-center">
                <a href="book_chapter_04.html" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center">
                    <i class="fas fa-book-open mr-2"></i>
                    عرض الفصل المطور
                </a>
            </div>
        </div>
    </div>
</body>
</html>